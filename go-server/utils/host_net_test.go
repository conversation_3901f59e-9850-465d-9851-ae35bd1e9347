package utils

import (
	"fmt"
	"sort"
	"strings"
	"testing"

	"mediacomm.com/skylink/model/system"
)

func TestGenerateFixedIP(t *testing.T) {
	// 这个测试需要在实际的容器环境中运行，因为需要访问/host/sys
	t.Skip("Skipping TestGenerateFixedIP - requires container environment with /host/sys mounted")
}

// TestGenerateFixedIPLogic 测试固定IP生成的逻辑
func TestGenerateFixedIPLogic(t *testing.T) {
	// 模拟网卡列表
	mockInterfaces := []string{"enaphyt4i1", "enaphyt4i0", "eth0", "eth1"}

	tests := []struct {
		name          string
		interfaceName string
		interfaces    []string
		wantIP        string
		wantErr       bool
	}{
		{
			name:          "First interface after sorting",
			interfaceName: "enaphyt4i0",
			interfaces:    mockInterfaces,
			wantIP:        "***********/24", // position 0 + 10 = 10
			wantErr:       false,
		},
		{
			name:          "Second interface after sorting",
			interfaceName: "enaphyt4i1",
			interfaces:    mockInterfaces,
			wantIP:        "***********/24", // position 1 + 10 = 11
			wantErr:       false,
		},
		{
			name:          "Third interface after sorting",
			interfaceName: "eth0",
			interfaces:    mockInterfaces,
			wantIP:        "***********/24", // position 2 + 10 = 12
			wantErr:       false,
		},
		{
			name:          "Fourth interface after sorting",
			interfaceName: "eth1",
			interfaces:    mockInterfaces,
			wantIP:        "***********/24", // position 3 + 10 = 13
			wantErr:       false,
		},
		{
			name:          "Non-existent interface",
			interfaceName: "nonexistent",
			interfaces:    mockInterfaces,
			wantIP:        "",
			wantErr:       true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 模拟generateFixedIP的逻辑
			interfaces := make([]string, len(tt.interfaces))
			copy(interfaces, tt.interfaces)

			// 对网卡名进行字符排序
			sort.Strings(interfaces)

			// 找到当前网卡在排序后的位置
			var position int = -1
			for i, iface := range interfaces {
				if iface == tt.interfaceName {
					position = i
					break
				}
			}

			var got string
			var err error
			if position == -1 {
				err = fmt.Errorf("interface %s not found in physical interfaces", tt.interfaceName)
			} else {
				thirdOctet := position + 10
				got = fmt.Sprintf("10.10.%d.10/24", thirdOctet)
			}

			if (err != nil) != tt.wantErr {
				t.Errorf("generateFixedIP logic error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.wantIP {
				t.Errorf("generateFixedIP logic = %v, want %v", got, tt.wantIP)
			}
			if !tt.wantErr {
				t.Logf("Generated fixed IP for %s: %s (sorted interfaces: %v)", tt.interfaceName, got, interfaces)
			}
		})
	}
}

func TestDetectNetworkManager(t *testing.T) {
	manager, err := DetectNetworkManager()
	if err != nil {
		t.Logf("DetectNetworkManager() error = %v", err)
	} else {
		t.Logf("Detected network manager: %s", manager)
	}
}

func TestApplyHostNetworkConfig(t *testing.T) {
	// 这个测试需要在实际的容器环境中运行
	t.Skip("Skipping integration test - requires container environment with nsenter")

	configs := []system.NetworkInterface{
		{
			Name:    "enaphyt4i0",
			DHCP:    false,
			Address: "*************",
			Netmask: "*************",
			Gateway: "***********",
		},
	}

	err := ApplyHostNetworkConfig(configs)
	if err != nil {
		t.Errorf("ApplyHostNetworkConfig() error = %v", err)
	}
}

func TestNetmaskToCIDR(t *testing.T) {
	tests := []struct {
		name    string
		mask    string
		want    int
		wantErr bool
	}{
		{
			name:    "Valid /24 netmask",
			mask:    "*************",
			want:    24,
			wantErr: false,
		},
		{
			name:    "Valid /16 netmask",
			mask:    "***********",
			want:    16,
			wantErr: false,
		},
		{
			name:    "Valid /8 netmask",
			mask:    "*********",
			want:    8,
			wantErr: false,
		},
		{
			name:    "Invalid netmask",
			mask:    "invalid",
			want:    0,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := netmaskToCIDR(tt.mask)
			if (err != nil) != tt.wantErr {
				t.Errorf("netmaskToCIDR() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("netmaskToCIDR() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestApplyWithIfupdownDebian(t *testing.T) {
	// 这个测试主要验证配置文件内容的生成逻辑，不会实际执行 nsenter 命令
	t.Log("Testing ifupdown configuration generation logic...")

	// 测试数据
	configs := map[string][]system.NetworkInterface{
		"eth0": {
			{
				Name:    "eth0",
				DHCP:    false,
				Address: "*************",
				Netmask: "*************",
				Gateway: "***********",
			},
		},
		"eth1": {
			{
				Name:    "eth1",
				DHCP:    true,
				Address: "",
				Netmask: "",
				Gateway: "",
			},
		},
		"eth2": {
			{
				Name:    "eth2",
				DHCP:    false,
				Address: "**********",
				Netmask: "*************",
				Gateway: "",
			},
			{
				Name:    "eth2",
				DHCP:    false,
				Address: "**********",
				Netmask: "*************",
				Gateway: "********",
			},
		},
	}

	// 测试静态IP配置生成
	testIfupdownStaticConfig(t, configs["eth0"])

	// 测试DHCP配置生成
	testIfupdownDHCPConfig(t, configs["eth1"])

	// 测试多IP配置生成
	testIfupdownMultiIPConfig(t, configs["eth2"])
}

func testIfupdownStaticConfig(t *testing.T, configs []system.NetworkInterface) {
	t.Log("Testing static IP configuration generation...")

	// 模拟 applyWithIfupdownDebian 中的配置生成逻辑
	iface := configs[0].Name
	var configContent strings.Builder
	configContent.WriteString(fmt.Sprintf("# Configuration for %s\n", iface))
	configContent.WriteString(fmt.Sprintf("auto %s\n", iface))

	if !configs[0].DHCP {
		configContent.WriteString(fmt.Sprintf("iface %s inet static\n", iface))

		// 添加用户配置的IP地址
		aliasIndex := 0
		for _, config := range configs {
			if config.Address != "" && config.Netmask != "" {
				if aliasIndex == 0 {
					// 第一个用户IP作为主IP
					configContent.WriteString(fmt.Sprintf("    address %s\n", config.Address))
					configContent.WriteString(fmt.Sprintf("    netmask %s\n", config.Netmask))
				} else {
					// 其他IP作为别名接口
					configContent.WriteString(fmt.Sprintf("\nauto %s:%d\n", iface, aliasIndex))
					configContent.WriteString(fmt.Sprintf("iface %s:%d inet static\n", iface, aliasIndex))
					configContent.WriteString(fmt.Sprintf("    address %s\n", config.Address))
					configContent.WriteString(fmt.Sprintf("    netmask %s\n", config.Netmask))
				}
				aliasIndex++
			}
		}

		// 添加网关（以最后一个非空的网关为准）
		var gateway string
		for _, config := range configs {
			if config.Gateway != "" {
				gateway = config.Gateway
			}
		}
		if gateway != "" {
			configContent.WriteString(fmt.Sprintf("    gateway %s\n", gateway))
		}

		configContent.WriteString("    dns-nameservers ******* ***************\n")
	}

	result := configContent.String()
	t.Logf("Generated static config for %s:\n%s", iface, result)

	// 验证配置内容
	if !strings.Contains(result, "iface eth0 inet static") {
		t.Error("Static configuration should contain 'iface eth0 inet static'")
	}
	if !strings.Contains(result, "address *************") {
		t.Error("Static configuration should contain correct IP address")
	}
	if !strings.Contains(result, "netmask *************") {
		t.Error("Static configuration should contain correct netmask")
	}
	if !strings.Contains(result, "gateway ***********") {
		t.Error("Static configuration should contain correct gateway")
	}
	if !strings.Contains(result, "dns-nameservers ******* ***************") {
		t.Error("Static configuration should contain DNS nameservers")
	}
}

func testIfupdownDHCPConfig(t *testing.T, configs []system.NetworkInterface) {
	t.Log("Testing DHCP configuration generation...")

	iface := configs[0].Name
	var configContent strings.Builder
	configContent.WriteString(fmt.Sprintf("# Configuration for %s\n", iface))
	configContent.WriteString(fmt.Sprintf("auto %s\n", iface))

	if configs[0].DHCP {
		configContent.WriteString(fmt.Sprintf("iface %s inet dhcp\n", iface))
	}

	result := configContent.String()
	t.Logf("Generated DHCP config for %s:\n%s", iface, result)

	// 验证配置内容
	if !strings.Contains(result, "iface eth1 inet dhcp") {
		t.Error("DHCP configuration should contain 'iface eth1 inet dhcp'")
	}
}

func testIfupdownMultiIPConfig(t *testing.T, configs []system.NetworkInterface) {
	t.Log("Testing multi-IP configuration generation...")

	iface := configs[0].Name
	var configContent strings.Builder
	configContent.WriteString(fmt.Sprintf("# Configuration for %s\n", iface))
	configContent.WriteString(fmt.Sprintf("auto %s\n", iface))

	if !configs[0].DHCP {
		configContent.WriteString(fmt.Sprintf("iface %s inet static\n", iface))

		// 添加用户配置的IP地址（作为别名）
		aliasIndex := 0
		var gateway string
		for _, config := range configs {
			if config.Address != "" && config.Netmask != "" {
				if aliasIndex == 0 {
					// 第一个IP作为主IP
					configContent.WriteString(fmt.Sprintf("    address %s\n", config.Address))
					configContent.WriteString(fmt.Sprintf("    netmask %s\n", config.Netmask))
				} else {
					// 其他IP作为别名接口
					configContent.WriteString(fmt.Sprintf("\nauto %s:%d\n", iface, aliasIndex))
					configContent.WriteString(fmt.Sprintf("iface %s:%d inet static\n", iface, aliasIndex))
					configContent.WriteString(fmt.Sprintf("    address %s\n", config.Address))
					configContent.WriteString(fmt.Sprintf("    netmask %s\n", config.Netmask))
				}
				aliasIndex++
			}
			// 以最后一个非空的网关为准
			if config.Gateway != "" {
				gateway = config.Gateway
			}
		}

		if gateway != "" {
			configContent.WriteString(fmt.Sprintf("    gateway %s\n", gateway))
		}
		configContent.WriteString("    dns-nameservers ******* ***************\n")
	}

	result := configContent.String()
	t.Logf("Generated multi-IP config for %s:\n%s", iface, result)

	// 验证配置内容
	if !strings.Contains(result, "iface eth2 inet static") {
		t.Error("Multi-IP configuration should contain 'iface eth2 inet static'")
	}
	if !strings.Contains(result, "address **********") {
		t.Error("Multi-IP configuration should contain first IP address")
	}
	if !strings.Contains(result, "iface eth2:1 inet static") {
		t.Error("Multi-IP configuration should contain alias interface")
	}
	if !strings.Contains(result, "address **********") {
		t.Error("Multi-IP configuration should contain second IP address")
	}
	if !strings.Contains(result, "gateway ********") {
		t.Error("Multi-IP configuration should contain gateway")
	}
}

func TestGetNetworkInterfaceConfigs(t *testing.T) {
	// 这个测试需要在实际的容器环境中运行
	t.Skip("Skipping integration test - requires container environment with nsenter")

	configs, err := GetNetworkInterfaceConfigs()
	if err != nil {
		t.Errorf("GetNetworkInterfaceConfigs() error = %v", err)
		return
	}

	t.Logf("Found %d network interfaces", len(configs))
	for _, config := range configs {
		t.Logf("Interface: %s, DHCP: %v, Address: %s, Netmask: %s, Gateway: %s",
			config.Name, config.DHCP, config.Address, config.Netmask, config.Gateway)
	}
}

func TestParseNMDeviceInfo(t *testing.T) {
	output := `GENERAL.DEVICE:                         eth0
GENERAL.TYPE:                           ethernet
GENERAL.HWADDR:                         00:15:5D:01:02:03
GENERAL.MTU:                            1500
GENERAL.STATE:                          100 (connected)
GENERAL.CONNECTION:                     Wired connection 1
GENERAL.CON-PATH:                       /org/freedesktop/NetworkManager/ActiveConnection/1
IP4.ADDRESS[1]:                         *************/24
IP4.GATEWAY:                            ***********
IP4.ROUTE[1]:                           dst = 0.0.0.0/0, nh = ***********, mt = 100
IP4.DNS[1]:                             ***********`

	config := &system.NetworkInterface{Name: "eth0"}
	parseNMDeviceInfo(output, config)

	if config.Address != "*************" {
		t.Errorf("Expected address *************, got %s", config.Address)
	}
	if config.Netmask != "*************" {
		t.Errorf("Expected netmask *************, got %s", config.Netmask)
	}
	if config.Gateway != "***********" {
		t.Errorf("Expected gateway ***********, got %s", config.Gateway)
	}
}

func TestParseNMConnectionInfo(t *testing.T) {
	output := `connection.id:                          Wired connection 1
connection.uuid:                        12345678-1234-1234-1234-123456789abc
connection.stable-id:                   --
connection.type:                        802-3-ethernet
connection.interface-name:              eth0
ipv4.method:                            manual
ipv4.dns:                               *******,*******
ipv4.dns-search:                        --
ipv4.dns-options:                       --
ipv4.dns-priority:                      0
ipv4.addresses:                         *************/24
ipv4.gateway:                           ***********
ipv4.routes:                            --`

	config := &system.NetworkInterface{Name: "eth0"}
	parseNMConnectionInfo(output, config)

	if config.DHCP != false {
		t.Errorf("Expected DHCP false, got %v", config.DHCP)
	}
	if config.Address != "*************" {
		t.Errorf("Expected address *************, got %s", config.Address)
	}
	if config.Netmask != "*************" {
		t.Errorf("Expected netmask *************, got %s", config.Netmask)
	}
	if config.Gateway != "***********" {
		t.Errorf("Expected gateway ***********, got %s", config.Gateway)
	}
}

func TestParseIfupdownConfig(t *testing.T) {
	output := `# This file describes the network interfaces available on your system
# and how to activate them. For more information, see interfaces(5).

source /etc/network/interfaces.d/*

# The loopback network interface
auto lo
iface lo inet loopback

# The primary network interface
auto eth0
iface eth0 inet static
    address *************
    netmask *************
    gateway ***********
    dns-nameservers ******* *******

auto eth1
iface eth1 inet dhcp`

	config := &system.NetworkInterface{Name: "eth0"}
	parseIfupdownConfig(output, "eth0", config)

	if config.DHCP != false {
		t.Errorf("Expected DHCP false, got %v", config.DHCP)
	}
	if config.Address != "*************" {
		t.Errorf("Expected address *************, got %s", config.Address)
	}
	if config.Netmask != "*************" {
		t.Errorf("Expected netmask *************, got %s", config.Netmask)
	}
	if config.Gateway != "***********" {
		t.Errorf("Expected gateway ***********, got %s", config.Gateway)
	}

	// 测试 DHCP 配置
	config2 := &system.NetworkInterface{Name: "eth1"}
	parseIfupdownConfig(output, "eth1", config2)

	if config2.DHCP != true {
		t.Errorf("Expected DHCP true for eth1, got %v", config2.DHCP)
	}
}

func TestParseIPAddrOutput(t *testing.T) {
	output := `2: eth0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc pfifo_fast state UP group default qlen 1000
    link/ether 00:15:5d:01:02:03 brd ff:ff:ff:ff:ff:ff
    inet *************/24 brd ************* scope global eth0
       valid_lft forever preferred_lft forever
    inet6 fe80::215:5dff:fe01:203/64 scope link
       valid_lft forever preferred_lft forever`

	config := &system.NetworkInterface{Name: "eth0"}
	parseIPAddrOutput(output, config)

	if config.Address != "*************" {
		t.Errorf("Expected address *************, got %s", config.Address)
	}
	if config.Netmask != "*************" {
		t.Errorf("Expected netmask *************, got %s", config.Netmask)
	}
}

func TestParseIPRouteOutput(t *testing.T) {
	output := `default via *********** dev eth0 proto dhcp metric 100
***********/24 dev eth0 proto kernel scope link src ************* metric 100`

	config := &system.NetworkInterface{Name: "eth0"}
	parseIPRouteOutput(output, config)

	if config.Gateway != "***********" {
		t.Errorf("Expected gateway ***********, got %s", config.Gateway)
	}
}

func TestIsFixedIP(t *testing.T) {
	tests := []struct {
		name     string
		address  string
		expected bool
	}{
		{
			name:     "Valid fixed IP - ***********",
			address:  "***********",
			expected: true,
		},
		{
			name:     "Valid fixed IP - ***********",
			address:  "***********",
			expected: true,
		},
		{
			name:     "Valid fixed IP - ************",
			address:  "************",
			expected: true,
		},
		{
			name:     "Invalid - wrong first octet",
			address:  "************",
			expected: false,
		},
		{
			name:     "Invalid - wrong second octet",
			address:  "************",
			expected: false,
		},
		{
			name:     "Invalid - wrong last octet",
			address:  "***********0",
			expected: false,
		},
		{
			name:     "Invalid - third octet too small",
			address:  "**********",
			expected: false,
		},
		{
			name:     "Invalid - empty address",
			address:  "",
			expected: false,
		},
		{
			name:     "Invalid - malformed IP",
			address:  "10.10.10",
			expected: false,
		},
		{
			name:     "Regular IP address",
			address:  "*************",
			expected: false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := isFixedIP(test.address)
			if result != test.expected {
				t.Errorf("For address %s, expected %v, got %v", test.address, test.expected, result)
			}
		})
	}
}

func TestFilterFixedIPs(t *testing.T) {
	configs := []system.NetworkInterface{
		{
			Name:    "eth0",
			Address: "***********",
			Netmask: "*************",
			DHCP:    false,
		},
		{
			Name:    "eth0",
			Address: "*************",
			Netmask: "*************",
			DHCP:    false,
		},
		{
			Name:    "eth1",
			Address: "***********",
			Netmask: "*************",
			DHCP:    false,
		},
		{
			Name:    "eth1",
			Address: "**********",
			Netmask: "*************",
			DHCP:    false,
		},
		{
			Name: "eth2",
			DHCP: true,
		},
	}

	result := filterFixedIPs(configs)

	// 应该过滤掉 *********** 和 ***********，保留其他配置
	expectedCount := 3
	if len(result) != expectedCount {
		t.Errorf("Expected %d configs after filtering, got %d", expectedCount, len(result))
	}

	// 检查保留的配置
	expectedAddresses := []string{"*************", "**********", ""}
	for i, config := range result {
		if i < len(expectedAddresses) && config.Address != expectedAddresses[i] {
			t.Errorf("Expected address %s at index %d, got %s", expectedAddresses[i], i, config.Address)
		}
	}

	// 确保没有固定IP
	for _, config := range result {
		if isFixedIP(config.Address) {
			t.Errorf("Fixed IP %s should have been filtered out", config.Address)
		}
	}
}
