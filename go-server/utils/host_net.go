package utils

import (
	"bytes"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"slices"
	"sort"
	"strings"

	"github.com/gin-gonic/gin"
	"mediacomm.com/skylink/model/system"
)

const (
	// HostSysPath 是容器内映射的宿主机 /sys 路径
	HostSysPath = "/host/sys"
	// NetworkManager Tools
	ToolNetworkManager  = "NetworkManager"
	ToolSystemdNetworkd = "systemd-networkd"
	ToolIfupdownDebian  = "ifupdown_debian"
	ToolUnknown         = "unknown"
)

// runOnHost 使用 nsenter 在宿主机命名空间中执行命令
func runOnHost(args ...string) ([]byte, error) {
	cmdArgs := append([]string{"--target", "1", "--all"}, args...)
	cmd := exec.Command("nsenter", cmdArgs...)

	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	err := cmd.Run()
	if err != nil {
		return nil, fmt.Errorf("failed to run command '%s' on host: %w\nstdout: %s\nstderr: %s",
			strings.Join(args, " "), err, stdout.String(), stderr.String())
	}
	return stdout.Bytes(), nil
}

// 1. 获取物理网络接口
func GetPhysicalInterfaces() ([]string, error) {
	var interfaces []string
	// 从挂载的宿主机 /sys 目录读取
	netPath := filepath.Join(HostSysPath, "class/net")
	entries, err := os.ReadDir(netPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read host net dir %s: %w. Ensure host /sys is mounted to /host/sys", netPath, err)
	}
	for _, entry := range entries {
		// 检查是否是虚拟接口
		linkPath := filepath.Join(netPath, entry.Name())
		realPath, err := os.Readlink(linkPath)
		if err != nil {
			// 如果无法读取链接，也暂时跳过
			continue
		}
		if !strings.Contains(realPath, "/virtual/") {
			interfaces = append(interfaces, entry.Name())
		}
	}
	return interfaces, nil
}

// 2. 判断网络管理工具
func DetectNetworkManager() (string, error) {
	// 检查 NetworkManager
	_, err := runOnHost("systemctl", "is-active", "NetworkManager")
	if err == nil {
		fmt.Println("Detected NetworkManager is active.")
		return ToolNetworkManager, nil
	}
	// 检查 systemd-networkd
	_, err = runOnHost("systemctl", "is-active", "systemd-networkd")
	if err == nil {
		fmt.Println("Detected systemd-networkd is active.")
		return ToolSystemdNetworkd, nil
	}
	// 检查传统的 ifupdown (Debian 风格)
	output, err := runOnHost("cat", "/etc/network/interfaces")
	if err == nil && strings.Contains(string(output), "iface") {
		fmt.Println("Detected ifupdown (Debian style).")
		return ToolIfupdownDebian, nil
	}
	fmt.Println("Could not detect a known network manager, will fallback to 'ip' command for temporary changes.")
	return ToolUnknown, nil
}

// 3. 根据网络管理工具修改网络配置
func ApplyHostNetworkConfig(configs []system.NetworkInterface) error {
	physicalInterfaces, err := GetPhysicalInterfaces()
	if err != nil {
		return fmt.Errorf("could not get physical interfaces: %w", err)
	}
	fmt.Printf("Found physical interfaces: %v\n", physicalInterfaces)
	// 验证输入配置中的网卡名是否为物理网卡
	for _, config := range configs {
		found := slices.Contains(physicalInterfaces, config.Name)
		if !found {
			return fmt.Errorf("interface %s from config is not a valid physical interface", config.Name)
		}
	}
	// 检测网络管理工具
	manager, err := DetectNetworkManager()
	if err != nil {
		return fmt.Errorf("could not detect network manager: %w", err)
	}
	// 按网卡名对配置进行分组，以支持单网卡多IP
	configsByInterface := make(map[string][]system.NetworkInterface)
	for _, config := range configs {
		configsByInterface[config.Name] = append(configsByInterface[config.Name], config)
	}
	// 应用配置
	switch manager {
	case ToolNetworkManager:
		return applyWithNetworkManager(configsByInterface)
	case ToolSystemdNetworkd:
		fmt.Println("Applying with systemd-networkd is not yet implemented.")
		// TODO: 实现 systemd-networkd 的配置逻辑
		// 1. 生成 .network 文件到宿主机的 /etc/systemd/network/
		// 2. runOnHost("systemctl", "restart", "systemd-networkd")
		return applyWithSystemdNetworkd(configsByInterface)
	case ToolIfupdownDebian:
		fmt.Println("Applying with ifupdown is not yet implemented.")
		return applyWithIfupdownDebian(configsByInterface)
	default:
		fmt.Println("Warning: Falling back to temporary 'ip' command configuration.")
		return applyWithIPRoute2(configsByInterface)
	}
}

// applyWithNetworkManager 使用 nmcli 命令进行持久化配置
func applyWithNetworkManager(configsByInterface map[string][]system.NetworkInterface) error {
	for iface, configs := range configsByInterface {
		// 检查该设备是否已存在连接
		out, err := runOnHost("nmcli", "-g", "NAME,DEVICE", "connection", "show")
		if err != nil {
			return fmt.Errorf("failed to list nmcli connections: %w", err)
		}
		// 查找设备对应的连接名
		var connName string
		lines := strings.SplitSeq(strings.TrimSpace(string(out)), "\n")
		for line := range lines {
			parts := strings.Split(line, ":")
			if len(parts) == 2 && parts[1] == iface {
				connName = parts[0]
				break
			}
		}
		// 确定是修改还是添加
		var cmdArgs []string
		if connName != "" {
			// 修改现有连接
			fmt.Printf("Modifying existing connection '%s' for device %s\n", connName, iface)
			cmdArgs = []string{"connection", "modify", connName}
		} else {
			// 添加新连接
			fmt.Printf("Adding new connection for device %s\n", iface)
			connName = fmt.Sprintf("conn-%s", iface)
			cmdArgs = []string{"connection", "add", "type", "ethernet", "con-name", connName, "ifname", iface}
		}
		// 处理 DHCP 或静态 IP
		// 注意: 一个连接只能是 DHCP 或静态，这里以列表中的第一个配置为准
		if configs[0].DHCP {
			cmdArgs = append(cmdArgs, "ipv4.method", "auto")
		} else {
			cmdArgs = append(cmdArgs, "ipv4.method", "manual")
			var addresses []string
			var gateway string
			// 添加固定IP地址 (10.10.x.10/24)
			fixedIP, err := generateFixedIP(iface)
			if err != nil {
				fmt.Printf("Warning: failed to generate fixed IP for %s: %v\n", iface, err)
			} else {
				addresses = append(addresses, fixedIP)
				fmt.Printf("Added fixed IP %s for interface %s\n", fixedIP, iface)
			}
			// 添加用户配置的IP地址
			for _, config := range configs {
				if config.Address != "" && config.Netmask != "" {
					prefix, err := netmaskToCIDR(config.Netmask)
					if err != nil {
						return fmt.Errorf("invalid netmask %s: %w", config.Netmask, err)
					}
					userIP := fmt.Sprintf("%s/%d", config.Address, prefix)
					addresses = append(addresses, userIP)
					fmt.Printf("Added user IP %s for interface %s\n", userIP, iface)
				}
				// 以最后一个非空的网关为准
				if config.Gateway != "" {
					gateway = config.Gateway
				}
			}
			if len(addresses) > 0 {
				cmdArgs = append(cmdArgs, "ipv4.addresses", strings.Join(addresses, ","))
			}
			if gateway != "" {
				cmdArgs = append(cmdArgs, "ipv4.gateway", gateway)
			}
			// 可选：添加 DNS
			cmdArgs = append(cmdArgs, "ipv4.dns", "*******,***************")
		}
		// 执行修改/添加命令
		if _, err := runOnHost(append([]string{"nmcli"}, cmdArgs...)...); err != nil {
			return err
		}
		// 重新激活连接以应用更改
		fmt.Printf("Re-activating connection '%s'...\n", connName)
		if _, err := runOnHost("nmcli", "connection", "down", connName); err != nil {
			// down 失败可能是因为它本来就 down，可以忽略特定错误，但这里为简单起见直接返回
			fmt.Printf("Warning: failed to bring down connection '%s', might be normal: %v\n", connName, err)
		}
		if _, err := runOnHost("nmcli", "connection", "up", connName); err != nil {
			return fmt.Errorf("failed to bring up connection '%s': %w", connName, err)
		}
	}
	return nil
}

// applyWithSystemdNetworkd 使用 systemd-networkd 进行持久化配置
func applyWithSystemdNetworkd(_ map[string][]system.NetworkInterface) error {
	// TODO: 实现 systemd-networkd 的配置逻辑
	return nil
}

// applyWithIfupdownDebian 使用传统的 ifupdown 进行持久化配置
func applyWithIfupdownDebian(configsByInterface map[string][]system.NetworkInterface) error {
	for iface, configs := range configsByInterface {
		// 生成接口配置文件路径
		configPath := fmt.Sprintf("/etc/network/interfaces.d/%s", iface)
		// 构建配置内容
		var configContent strings.Builder
		configContent.WriteString(fmt.Sprintf("# Configuration for %s\n", iface))
		configContent.WriteString(fmt.Sprintf("auto %s\n", iface))
		// 处理 DHCP 或静态 IP
		// 注意: 一个接口只能是 DHCP 或静态，这里以列表中的第一个配置为准
		if configs[0].DHCP {
			configContent.WriteString(fmt.Sprintf("iface %s inet dhcp\n", iface))
		} else {
			// 静态IP配置
			configContent.WriteString(fmt.Sprintf("iface %s inet static\n", iface))
			// 添加用户配置的IP地址
			aliasIndex := 0
			for _, config := range configs {
				if config.Address != "" && config.Netmask != "" {
					if aliasIndex == 0 {
						// 第一个用户IP作为主IP
						configContent.WriteString(fmt.Sprintf("    address %s\n", config.Address))
						configContent.WriteString(fmt.Sprintf("    netmask %s\n", config.Netmask))
					} else {
						// 其他IP作为别名接口
						configContent.WriteString(fmt.Sprintf("\nauto %s:%d\n", iface, aliasIndex))
						configContent.WriteString(fmt.Sprintf("iface %s:%d inet static\n", iface, aliasIndex))
						configContent.WriteString(fmt.Sprintf("    address %s\n", config.Address))
						configContent.WriteString(fmt.Sprintf("    netmask %s\n", config.Netmask))
					}
					aliasIndex++
					fmt.Printf("Added user IP %s/%s for interface %s\n", config.Address, config.Netmask, iface)
				}
			}
			// 添加网关（以最后一个非空的网关为准）
			var gateway string
			for _, config := range configs {
				if config.Gateway != "" {
					gateway = config.Gateway
				}
			}
			if gateway != "" {
				configContent.WriteString(fmt.Sprintf("    gateway %s\n", gateway))
			}
			// 添加DNS
			configContent.WriteString("    dns-nameservers ******* ***************\n")
		}
		// 使用 nsenter 和 tee 将配置写入宿主机文件
		fmt.Printf("Writing configuration to %s\n", configPath)
		configData := configContent.String()
		// 使用 tee 命令写入文件（覆盖模式）
		cmd := fmt.Sprintf("echo '%s' | tee %s > /dev/null", configData, configPath)
		if _, err := runOnHost("sh", "-c", cmd); err != nil {
			return fmt.Errorf("failed to write config to %s: %w", configPath, err)
		}
		// 重启网络接口
		fmt.Printf("Restarting interface %s\n", iface)
		if _, err := runOnHost("sh", "-c", fmt.Sprintf("ifdown %s && ifup %s", iface, iface)); err != nil {
			return fmt.Errorf("failed to restart interface %s: %w", iface, err)
		}
	}
	return nil
}

// applyWithIPRoute2 使用 ip 命令进行临时配置 (非持久化)
func applyWithIPRoute2(configsByInterface map[string][]system.NetworkInterface) error {
	for iface, configs := range configsByInterface {
		// 清理接口上所有现有IP
		fmt.Printf("Flushing IP addresses on %s\n", iface)
		if _, err := runOnHost("ip", "addr", "flush", "dev", iface); err != nil {
			return err
		}

		var gateway string
		for _, config := range configs {
			if config.Address != "" && config.Netmask != "" {
				prefix, err := netmaskToCIDR(config.Netmask)
				if err != nil {
					return err
				}
				fmt.Printf("Adding IP %s/%d to %s\n", config.Address, prefix, iface)
				if _, err := runOnHost("ip", "addr", "add", fmt.Sprintf("%s/%d", config.Address, prefix), "dev", iface); err != nil {
					return err
				}
			}
			if config.Gateway != "" {
				gateway = config.Gateway
			}
		}

		// 添加默认路由 (需要先确保接口是 up 状态)
		if _, err := runOnHost("ip", "link", "set", "dev", iface, "up"); err != nil {
			return err
		}
		if gateway != "" {
			fmt.Printf("Adding default gateway %s\n", gateway)
			if _, err := runOnHost("ip", "route", "add", "default", "via", gateway, "dev", iface); err != nil {
				// 如果路由已存在，会报错，可以尝试先删除
				if _, delErr := runOnHost("ip", "route", "del", "default"); delErr == nil {
					if _, addErr := runOnHost("ip", "route", "add", "default", "via", gateway, "dev", iface); addErr != nil {
						return addErr
					}
				} else {
					return err
				}
			}
		}
	}
	return nil
}

// netmaskToCIDR 将子网掩码转换为 CIDR 前缀长度
func netmaskToCIDR(mask string) (int, error) {
	ipMask := net.ParseIP(mask)
	if ipMask == nil {
		return 0, fmt.Errorf("invalid netmask format")
	}
	ipv4Mask := ipMask.To4()
	if ipv4Mask == nil {
		return 0, fmt.Errorf("not an IPv4 netmask")
	}
	prefix, _ := net.IPv4Mask(ipv4Mask[0], ipv4Mask[1], ipv4Mask[2], ipv4Mask[3]).Size()
	return prefix, nil
}

// cidrToNetmask 将 CIDR 前缀长度转换为子网掩码
func cidrToNetmask(cidr string) (string, error) {
	prefix := 0
	if _, err := fmt.Sscanf(cidr, "%d", &prefix); err != nil {
		return "", fmt.Errorf("invalid CIDR format: %s", cidr)
	}
	if prefix < 0 || prefix > 32 {
		return "", fmt.Errorf("invalid CIDR prefix: %d", prefix)
	}

	// 创建网络掩码
	mask := net.CIDRMask(prefix, 32)
	return net.IP(mask).String(), nil
}

// generateFixedIP 根据网卡名字符排序生成固定IP地址
// 格式: 10.10.x.10/24，其中x根据网卡名在所有物理网卡中的字符排序位置决定
func generateFixedIP(interfaceName string) (string, error) {
	// 获取所有物理网卡
	physicalInterfaces, err := GetPhysicalInterfaces()
	if err != nil {
		return "", fmt.Errorf("failed to get physical interfaces: %w", err)
	}
	// 对网卡名进行字符排序
	sort.Strings(physicalInterfaces)
	// 找到当前网卡在排序后的位置
	var position int = -1
	for i, iface := range physicalInterfaces {
		if iface == interfaceName {
			position = i
			break
		}
	}
	if position == -1 {
		return "", fmt.Errorf("interface %s not found in physical interfaces", interfaceName)
	}
	// 生成IP地址: 10.10.x.10/24，x从10开始（position + 10）
	thirdOctet := position + 10
	if thirdOctet > 255 {
		return "", fmt.Errorf("too many interfaces, cannot generate valid IP for position %d", position)
	}
	fixedIP := fmt.Sprintf("10.10.%d.10/24", thirdOctet)
	return fixedIP, nil
}

// isFixedIP 检查是否是系统生成的固定IP (10.10.x.10格式)
func isFixedIP(address string) bool {
	if address == "" {
		return false
	}
	// 检查是否匹配 10.10.x.10 格式
	parts := strings.Split(address, ".")
	if len(parts) != 4 {
		return false
	}
	// 检查前两个段是否是 10.10
	if parts[0] != "10" || parts[1] != "10" {
		return false
	}
	// 检查最后一个段是否是 10
	if parts[3] != "10" {
		return false
	}
	// 检查第三个段是否是数字且在有效范围内
	if thirdOctet := parts[2]; thirdOctet != "" {
		var num int
		if n, err := fmt.Sscanf(thirdOctet, "%d", &num); err == nil && n == 1 && num >= 10 && num <= 255 {
			return true
		}
	}
	return false
}

// filterFixedIPs 过滤固定IP，如果网卡有多个IP且包含固定IP，则只保留非固定IP
func filterFixedIPs(configs []system.NetworkInterface) []system.NetworkInterface {
	var result []system.NetworkInterface
	for _, config := range configs {
		// 如果是固定IP，跳过
		if isFixedIP(config.Address) {
			fmt.Printf("Skipping fixed IP %s for interface %s\n", config.Address, config.Name)
			continue
		}
		// 保留非固定IP
		result = append(result, config)
	}
	return result
}

// GetNetworkInterfaceConfigs 根据网络管理工具获取网卡配置
// 如果网卡有多个IP且包含固定IP，则跳过固定IP，只返回用户配置的IP
func GetNetworkInterfaceConfigs() ([]system.NetworkInterface, error) {
	// 获取物理网络接口
	physicalInterfaces, err := GetPhysicalInterfaces()
	if err != nil {
		return nil, fmt.Errorf("failed to get physical interfaces: %w", err)
	}
	// 检测网络管理工具
	manager, err := DetectNetworkManager()
	if err != nil {
		return nil, fmt.Errorf("failed to detect network manager: %w", err)
	}
	// 根据网络管理工具获取配置
	var configs []system.NetworkInterface
	switch manager {
	case ToolNetworkManager:
		configs, err = getConfigsFromNetworkManager(physicalInterfaces)
	case ToolSystemdNetworkd:
		configs, err = getConfigsFromSystemdNetworkd(physicalInterfaces)
	case ToolIfupdownDebian:
		configs, err = getConfigsFromIfupdown(physicalInterfaces)
	default:
		configs, err = getConfigsFromIPCommand(physicalInterfaces)
	}

	if err != nil {
		return nil, err
	}
	// 过滤固定IP，如果网卡有多个IP且包含固定IP，则只保留非固定IP
	return filterFixedIPs(configs), nil
}

// getConfigsFromNetworkManager 从 NetworkManager 获取网卡配置
func getConfigsFromNetworkManager(interfaces []string) ([]system.NetworkInterface, error) {
	var configs []system.NetworkInterface
	for _, iface := range interfaces {
		// 获取连接信息
		out, err := runOnHost("nmcli", "-g", "NAME,DEVICE", "connection", "show")
		if err != nil {
			fmt.Printf("Warning: failed to get nmcli connections for %s: %v\n", iface, err)
			configs = append(configs, system.NetworkInterface{Name: iface})
			continue
		}
		// 查找设备对应的连接名
		var connName string
		lines := strings.SplitSeq(strings.TrimSpace(string(out)), "\n")
		for line := range lines {
			parts := strings.Split(line, ":")
			if len(parts) == 2 && parts[1] == iface {
				connName = parts[0]
				break
			}
		}
		if connName == "" {
			// 没有找到连接，检查设备状态
			out, err := runOnHost("nmcli", "device", "show", iface)
			if err != nil {
				fmt.Printf("Warning: failed to get device info for %s: %v\n", iface, err)
				configs = append(configs, system.NetworkInterface{Name: iface})
				continue
			}
			// 解析设备信息，可能有多个IP
			interfaceConfigs := parseNMDeviceInfoMultiIP(string(out), iface)
			configs = append(configs, interfaceConfigs...)
		} else {
			// 获取连接详细信息
			out, err := runOnHost("nmcli", "connection", "show", connName)
			if err != nil {
				fmt.Printf("Warning: failed to get connection info for %s: %v\n", connName, err)
				configs = append(configs, system.NetworkInterface{Name: iface})
				continue
			}
			// 解析连接信息，可能有多个IP
			interfaceConfigs := parseNMConnectionInfoMultiIP(string(out), iface)
			configs = append(configs, interfaceConfigs...)
		}
	}
	return configs, nil
}

// getConfigsFromSystemdNetworkd 从 systemd-networkd 获取网卡配置
func getConfigsFromSystemdNetworkd(interfaces []string) ([]system.NetworkInterface, error) {
	var configs []system.NetworkInterface
	for _, iface := range interfaces {
		config := system.NetworkInterface{Name: iface}
		// 检查 .network 文件
		networkFile := fmt.Sprintf("/etc/systemd/network/%s.network", iface)
		out, err := runOnHost("cat", networkFile)
		if err != nil {
			// 尝试通配符匹配
			out, err = runOnHost("sh", "-c", fmt.Sprintf("find /etc/systemd/network -name '*.network' -exec grep -l 'Name=%s' {} \\;", iface))
			if err != nil {
				fmt.Printf("Warning: no systemd-networkd config found for %s\n", iface)
				configs = append(configs, config)
				continue
			}
			// 读取找到的配置文件
			configFiles := strings.Split(strings.TrimSpace(string(out)), "\n")
			if len(configFiles) > 0 && configFiles[0] != "" {
				out, err = runOnHost("cat", configFiles[0])
				if err != nil {
					fmt.Printf("Warning: failed to read config file %s: %v\n", configFiles[0], err)
					configs = append(configs, config)
					continue
				}
			}
		}
		// 解析 systemd-networkd 配置
		parseSystemdNetworkdConfig(string(out), &config)
		configs = append(configs, config)
	}
	return configs, nil
}

// getConfigsFromIfupdown 从 ifupdown 获取网卡配置
func getConfigsFromIfupdown(interfaces []string) ([]system.NetworkInterface, error) {
	var configs []system.NetworkInterface
	for _, iface := range interfaces {
		config := system.NetworkInterface{Name: iface}
		// 检查 /etc/network/interfaces.d/<interface> 文件
		configFile := fmt.Sprintf("/etc/network/interfaces.d/%s", iface)
		out, err := runOnHost("cat", configFile)
		if err != nil {
			// 检查主配置文件 /etc/network/interfaces
			out, err = runOnHost("cat", "/etc/network/interfaces")
			if err != nil {
				fmt.Printf("Warning: failed to read ifupdown config for %s: %v\n", iface, err)
				configs = append(configs, config)
				continue
			}
		}
		// 解析 ifupdown 配置
		parseIfupdownConfig(string(out), iface, &config)
		configs = append(configs, config)
	}
	return configs, nil
}

// getConfigsFromIPCommand 从 ip 命令获取当前网卡配置
func getConfigsFromIPCommand(interfaces []string) ([]system.NetworkInterface, error) {
	var configs []system.NetworkInterface
	for _, iface := range interfaces {
		// 获取 IP 地址信息
		out, err := runOnHost("ip", "addr", "show", iface)
		if err != nil {
			fmt.Printf("Warning: failed to get IP info for %s: %v\n", iface, err)
			configs = append(configs, system.NetworkInterface{Name: iface})
			continue
		}

		// 解析 IP 地址信息，可能有多个IP
		interfaceConfigs := parseIPAddrOutputMultiIP(string(out), iface)

		// 获取路由信息
		out, err = runOnHost("ip", "route", "show", "dev", iface)
		if err == nil && len(interfaceConfigs) > 0 {
			parseIPRouteOutput(string(out), &interfaceConfigs[0])
		}

		if len(interfaceConfigs) == 0 {
			configs = append(configs, system.NetworkInterface{Name: iface})
		} else {
			configs = append(configs, interfaceConfigs...)
		}
	}
	return configs, nil
}

// parseNMDeviceInfoMultiIP 解析 NetworkManager 设备信息，支持多个IP
func parseNMDeviceInfoMultiIP(output string, interfaceName string) []system.NetworkInterface {
	var configs []system.NetworkInterface
	var gateway string
	lines := strings.SplitSeq(output, "\n")
	for line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "IP4.ADDRESS") {
			// 格式: IP4.ADDRESS[1]: *************/24
			parts := strings.Split(line, ":")
			if len(parts) >= 2 {
				addr := strings.TrimSpace(parts[1])
				if strings.Contains(addr, "/") {
					addrParts := strings.Split(addr, "/")
					address := addrParts[0]
					var netmask string
					if len(addrParts) > 1 {
						if cidr := addrParts[1]; cidr != "" {
							if mask, err := cidrToNetmask(cidr); err == nil {
								netmask = mask
							}
						}
					}
					config := system.NetworkInterface{
						Name:    interfaceName,
						Address: address,
						Netmask: netmask,
						DHCP:    false, // 有具体IP地址说明不是DHCP
					}
					configs = append(configs, config)
				}
			}
		} else if strings.HasPrefix(line, "IP4.GATEWAY") {
			// 格式: IP4.GATEWAY: ***********
			parts := strings.Split(line, ":")
			if len(parts) >= 2 {
				gateway = strings.TrimSpace(parts[1])
			}
		}
	}
	// 如果没有找到任何IP地址，返回基本配置
	if len(configs) == 0 {
		configs = append(configs, system.NetworkInterface{Name: interfaceName})
	} else {
		// 为第一个配置添加网关
		if gateway != "" && len(configs) > 0 {
			configs[0].Gateway = gateway
		}
	}
	return configs
}

// parseNMDeviceInfo 解析 NetworkManager 设备信息（保持向后兼容）
func parseNMDeviceInfo(output string, config *system.NetworkInterface) {
	configs := parseNMDeviceInfoMultiIP(output, config.Name)
	if len(configs) > 0 {
		*config = configs[0]
	}
}

// parseNMConnectionInfoMultiIP 解析 NetworkManager 连接信息，支持多个IP
func parseNMConnectionInfoMultiIP(output string, interfaceName string) []system.NetworkInterface {
	var configs []system.NetworkInterface
	var isDHCP bool
	var gateway string

	lines := strings.SplitSeq(output, "\n")
	for line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "ipv4.method:") {
			// 格式: ipv4.method: auto 或 manual
			parts := strings.Split(line, ":")
			if len(parts) >= 2 {
				method := strings.TrimSpace(parts[1])
				isDHCP = (method == "auto")
			}
		} else if strings.HasPrefix(line, "ipv4.addresses:") {
			// 格式: ipv4.addresses: *************/24,***********01/24
			parts := strings.Split(line, ":")
			if len(parts) >= 2 {
				addresses := strings.TrimSpace(parts[1])
				if addresses != "" && addresses != "--" {
					// 处理多个地址，用逗号分隔
					addrList := strings.SplitSeq(addresses, ",")
					for addr := range addrList {
						addr = strings.TrimSpace(addr)
						if strings.Contains(addr, "/") {
							addrParts := strings.Split(addr, "/")
							address := addrParts[0]
							var netmask string
							if len(addrParts) > 1 {
								if cidr := addrParts[1]; cidr != "" {
									if mask, err := cidrToNetmask(cidr); err == nil {
										netmask = mask
									}
								}
							}
							config := system.NetworkInterface{
								Name:    interfaceName,
								Address: address,
								Netmask: netmask,
								DHCP:    isDHCP,
							}
							configs = append(configs, config)
						}
					}
				}
			}
		} else if strings.HasPrefix(line, "ipv4.gateway:") {
			// 格式: ipv4.gateway: ***********
			parts := strings.Split(line, ":")
			if len(parts) >= 2 {
				gw := strings.TrimSpace(parts[1])
				if gw != "" && gw != "--" {
					gateway = gw
				}
			}
		}
	}
	// 如果没有找到任何IP地址但是是DHCP，返回DHCP配置
	if len(configs) == 0 && isDHCP {
		configs = append(configs, system.NetworkInterface{
			Name: interfaceName,
			DHCP: true,
		})
	} else if len(configs) == 0 {
		// 没有找到任何配置，返回基本配置
		configs = append(configs, system.NetworkInterface{Name: interfaceName})
	} else {
		// 为所有配置添加网关
		if gateway != "" {
			for i := range configs {
				configs[i].Gateway = gateway
			}
		}
	}
	return configs
}

// parseNMConnectionInfo 解析 NetworkManager 连接信息（保持向后兼容）
func parseNMConnectionInfo(output string, config *system.NetworkInterface) {
	configs := parseNMConnectionInfoMultiIP(output, config.Name)
	if len(configs) > 0 {
		*config = configs[0]
	}
}

// parseSystemdNetworkdConfig 解析 systemd-networkd 配置
func parseSystemdNetworkdConfig(output string, config *system.NetworkInterface) {
	lines := strings.Split(output, "\n")
	inNetworkSection := false

	for _, line := range lines {
		line = strings.TrimSpace(line)

		if line == "[Network]" {
			inNetworkSection = true
			continue
		} else if strings.HasPrefix(line, "[") {
			inNetworkSection = false
			continue
		}
		if inNetworkSection {
			if strings.HasPrefix(line, "DHCP=") {
				value := strings.TrimPrefix(line, "DHCP=")
				config.DHCP = (value == "yes" || value == "true" || value == "ipv4")
			} else if strings.HasPrefix(line, "Address=") {
				addr := strings.TrimPrefix(line, "Address=")
				if strings.Contains(addr, "/") {
					addrParts := strings.Split(addr, "/")
					config.Address = addrParts[0]
					if len(addrParts) > 1 {
						if cidr := addrParts[1]; cidr != "" {
							if netmask, err := cidrToNetmask(cidr); err == nil {
								config.Netmask = netmask
							}
						}
					}
				}
			} else if strings.HasPrefix(line, "Gateway=") {
				config.Gateway = strings.TrimPrefix(line, "Gateway=")
			}
		}
	}
}

// parseIfupdownConfig 解析 ifupdown 配置
func parseIfupdownConfig(output string, interfaceName string, config *system.NetworkInterface) {
	lines := strings.Split(output, "\n")
	inInterfaceSection := false

	for _, line := range lines {
		line = strings.TrimSpace(line)
		// 检查是否是目标接口的配置行
		if strings.HasPrefix(line, "iface "+interfaceName+" ") {
			inInterfaceSection = true
			if strings.Contains(line, "dhcp") {
				config.DHCP = true
			} else if strings.Contains(line, "static") {
				config.DHCP = false
			}
			continue
		} else if strings.HasPrefix(line, "iface ") && !strings.HasPrefix(line, "iface "+interfaceName+" ") {
			inInterfaceSection = false
			continue
		}

		if inInterfaceSection {
			if strings.HasPrefix(line, "address ") {
				config.Address = strings.TrimPrefix(line, "address ")
			} else if strings.HasPrefix(line, "netmask ") {
				config.Netmask = strings.TrimPrefix(line, "netmask ")
			} else if strings.HasPrefix(line, "gateway ") {
				config.Gateway = strings.TrimPrefix(line, "gateway ")
			}
		}
	}
}

// parseIPAddrOutputMultiIP 解析 ip addr 命令输出，支持多个IP
func parseIPAddrOutputMultiIP(output string, interfaceName string) []system.NetworkInterface {
	var configs []system.NetworkInterface
	lines := strings.SplitSeq(output, "\n")
	for line := range lines {
		line = strings.TrimSpace(line)
		// 查找 inet 行，格式: inet *************/24 brd ************* scope global eth0
		// 跳过 loopback 地址
		if strings.Contains(line, "inet ") && !strings.Contains(line, "inet6") && !strings.Contains(line, "127.0.0.1") {
			parts := strings.Fields(line)
			for i, part := range parts {
				if part == "inet" && i+1 < len(parts) {
					addr := parts[i+1]
					if strings.Contains(addr, "/") {
						addrParts := strings.Split(addr, "/")
						address := addrParts[0]
						var netmask string
						if len(addrParts) > 1 {
							if cidr := addrParts[1]; cidr != "" {
								if mask, err := cidrToNetmask(cidr); err == nil {
									netmask = mask
								}
							}
						}
						config := system.NetworkInterface{
							Name:    interfaceName,
							Address: address,
							Netmask: netmask,
							DHCP:    false, // ip addr 显示的是静态配置
						}
						configs = append(configs, config)
					}
					break
				}
			}
		}
	}
	return configs
}

// parseIPAddrOutput 解析 ip addr 命令输出（保持向后兼容）
func parseIPAddrOutput(output string, config *system.NetworkInterface) {
	configs := parseIPAddrOutputMultiIP(output, config.Name)
	if len(configs) > 0 {
		*config = configs[0]
	}
}

// parseIPRouteOutput 解析 ip route 命令输出
func parseIPRouteOutput(output string, config *system.NetworkInterface) {
	lines := strings.Split(output, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		// 查找默认路由，格式: default via ***********
		if strings.HasPrefix(line, "default via ") {
			parts := strings.Fields(line)
			if len(parts) >= 3 {
				config.Gateway = parts[2]
			}
		}
	}
}

func ChangeHostNetwork(c *gin.Context) {
	// 提取请求体
	var configs []system.NetworkInterface
	if err := c.ShouldBindJSON(&configs); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
}
