<template>
  <div class="app-container page">
    <div>
      <el-card class="card1">
        <span>系统统一入口</span>
      </el-card>
    </div>
    <el-card class="card2">
      <div style="width: 49.5%; height: auto; float: left; display: inline">
        <el-card class="card3" shadow="always">
          服务器
          <hr />
          <br />
          <el-row v-for="item in entryPointsData" :key="item.name" :gutter="20" style="padding: 5px">
            <el-container v-if="item.entry.length > 0">
              <!--              <el-aside class="aside" width="100px" :style=" {'background-image':item.icon} ">-->
              <el-aside class="aside" width="100px" :class="item.icon"> <br /><br /><br /><br />{{ item.name }} </el-aside>
              <el-main class="main">
                <el-row :gutter="20">
                  <el-space wrap>
                    <el-col v-for="point in item.entry" :key="point.Name" :span="8">
                      <el-button
                        style="background-color: #f2dcc8; border: 1px solid #f08519; width: 160px"
                        @click="toEntryPoint(point.IP, point.Port, point.Path)"
                      >
                        {{ point.Name }}
                      </el-button>
                    </el-col>
                  </el-space>
                </el-row>
              </el-main>
            </el-container>
          </el-row>
        </el-card>
      </div>

      <div style="width: 1%; height: auto; float: left; display: inline">&nbsp;</div>

      <div style="width: 49.5%; height: auto; float: left; display: inline">
        <el-card class="card3" shadow="always">
          客户端
          <hr />
          <br />

          <!--              <el-aside class="aside" width="100px" :class="item.icon">
                <br><br><br><br>{{ item.name }}
              </el-aside>-->
          <el-main class="main">
            <el-row :gutter="20">
              <el-space wrap>
                <el-col v-for="point in clients" :key="point.Name" :span="8">
                  <el-button style="background-color: #f2dcc8; border: 1px solid #f08519; width: 160px" @click="toEntryPoint(point.IP, point.Port, point.Path)">
                    {{ point.Name }}
                  </el-button>
                </el-col>
              </el-space>
            </el-row>
          </el-main>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  data() {
    return {
      backgroundImage: "", // 对应服务的入口图片
      // 容器服务入口
      entryPointsData: [
        {
          name: "集控平台",
          icon: "skylink",
          entry: [],
        },
        {
          name: "录播平台",
          icon: "director",
          entry: [],
        },
        {
          name: "窄带高分跨域指挥平台",
          icon: "narrow",
          entry: [],
        },
        {
          name: "云数据坐席协作管理平台",
          icon: "cloud",
          entry: [],
        },
      ],
      // 容器客户端
      clients: [],
    };
  },
  created() {
    this.getEntryPoint();
  },
  methods: {
    async toTarget(name) {
      console.log("跳转", name);
      await this.$router.push(`/${name}`);
    },

    /* 获取服务入口 */
    getEntryPoint() {
      this.$http.get(this.$SERVICE_PLATFORM.serverApi.entryPoints).then((res) => {
        res.data.forEach((point) => {
          if (point.Tags instanceof Array) {
            if (point.Tags.indexOf("服务端") >= 0) {
              this.entryPointsData.forEach((item) => {
                const index = point.Tags.indexOf(item.name);
                if (index >= 0) {
                  item.entry.push(point);
                }
              });
            } else if (point.Tags.indexOf("客户端") >= 0) {
              this.clients.push(point);
            }
          }
        });
      });
    },

    /* 打开容器服务入口的页面 */
    toEntryPoint(ip, port, path) {
      if (!ip) {
        ip = `${location.hostname}`;
      }
      const url = `http://${ip}:${port}${path}`;
      window.open(url, "_blank");
    },

    /*
     * 根据图标名称结合backgroundImage控制背景图标的显示.
     *
     * @param {string} icon 图标名称.
     * @return 存在该图标的定义时返回true.
     * */
    chooseBackgroundImage(icon) {
      console.log("点击", icon);
      switch (icon) {
        case "skylink":
          this.backgroundImage = "skylink";
          return true;
        case "director":
          this.backgroundImage = "director";
          return true;
        case "narrow":
          this.backgroundImage = "narrow";
          return true;
        case "cloud":
          this.backgroundImage = "cloud";
          return true;
        case "client":
          this.backgroundImage = "client";
          return true;
        default:
          return false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/style/icon.scss";

.card1 {
  background-color: #f08519; // 背景颜色
  padding: 0px;
  text-align: center; // 文本居中
  width: 100%;
  span {
    color: white; // 字体颜色
    font-weight: bold;
  }
}
.card2 {
  background-image: url("@/assets/IMG.jpg");
  height: 705px;
  width: 100%;
}
.card3 {
  text-align: center;
  font-weight: bolder;
  font-family: 微软雅黑;
  background-color: transparent; // 透明
  border-top: 1px solid #ffffff;
}
.aside {
  background-color: rgba(240, 133, 25, 0.9);
  //background-image: url("@/assets/icon/skylink_icon.png");
  background-repeat: no-repeat; // 取消平铺
  background-position: center; // 背景图片居中
  background-size: 20%; // 背景图片大小比例
  height: 130px;
  width: 140px;
  text-align: center;
  color: white;
}
.main {
  background-color: rgba(244, 244, 245, 0.7);
}

// 重写el-table的样式，更改为透明
/*.el-table {
  // 设置边框颜色及圆角
  border-radius: 10px 0 10px 10px;
  border: 1px ;
  // 取消背景颜色
  background-color: transparent;
  width: auto;
}*/
/*:deep(.el-table th) {
  background-color: transparent !important; // 取消表头选中高亮显示
  text-align: center;                       // 表头文本对齐方式：居中
  color: black;                             // 表头字体颜色：黑色
  font-size: 18px;                          // 表头字体大小
  border-right: 1px solid #ffffff;
  border-left: 1px solid #ffffff;
}*/
/*:deep(.el-table tr) {
  background-color: transparent !important; // 取消行选中高亮显示
}
:deep(.el-table td) {
  background-color: transparent !important; // 取消单元格选中高亮显示
  border: 1px solid #ffffff;
}*/

/* 表头间距
:deep(.el-table__header){
  -webkit-border-horizontal-spacing: 15px;  // 水平间距
}
 表内间距
:deep(.el-table__body){
  -webkit-border-horizontal-spacing: 15px;  // 水平间距
  -webkit-border-vertical-spacing: 15px;    // 垂直间距
}*/
</style>
