# 根据网络管理工具获取网卡配置

## 概述

本功能可以根据检测到的网络管理工具自动获取网卡配置信息，支持多种网络管理工具：

- NetworkManager (nmcli)
- systemd-networkd
- ifupdown (Debian/Ubuntu 传统方式)
- ip 命令 (fallback)

## 功能特性

### 1. 自动检测网络管理工具

系统会自动检测当前使用的网络管理工具，并使用相应的方法获取配置。

### 2. 固定 IP 过滤

如果网卡有多个 IP 地址且包含系统生成的固定 IP（10.10.x.10 格式），则自动跳过这些固定 IP，只返回用户配置的 IP 地址。

### 3. 统一的配置格式

无论使用哪种网络管理工具，都会返回统一的配置格式：

```json
[
  {
    "name": "eth0",
    "dhcp": false,
    "address": "*************",
    "netmask": "*************",
    "gateway": "***********"
  },
  {
    "name": "eth1",
    "dhcp": true,
    "address": "",
    "netmask": "",
    "gateway": ""
  }
]
```

### 3. 支持的网络管理工具

#### NetworkManager

- 使用 `nmcli` 命令获取连接和设备信息
- 支持解析连接配置和实时设备状态
- 自动识别 DHCP 和静态 IP 配置

#### systemd-networkd

- 读取 `/etc/systemd/network/*.network` 配置文件
- 支持通配符匹配网络配置文件
- 解析 `[Network]` 段的配置信息

#### ifupdown

- 读取 `/etc/network/interfaces.d/<interface>` 文件
- 支持解析主配置文件 `/etc/network/interfaces`
- 识别 `static` 和 `dhcp` 配置

#### ip 命令 (fallback)

- 使用 `ip addr show` 获取当前 IP 地址
- 使用 `ip route show` 获取路由信息
- 提供当前网络状态的快照

## API 使用示例

### 获取网卡配置

```bash
curl -X GET http://localhost:8080/v1/networks/interfaces
```

响应示例：

```json
[
  {
    "name": "enaphyt4i0",
    "dhcp": false,
    "address": "*************",
    "netmask": "*************",
    "gateway": "***********"
  },
  {
    "name": "enaphyt4i1",
    "dhcp": true,
    "address": "***********01",
    "netmask": "*************",
    "gateway": ""
  }
]
```

## 实现细节

### 1. NetworkManager 解析

#### 设备信息解析

从 `nmcli device show <interface>` 输出中解析：

- `IP4.ADDRESS[1]: *************/24` → 地址和网络掩码
- `IP4.GATEWAY: ***********` → 网关

#### 连接信息解析

从 `nmcli connection show <connection>` 输出中解析：

- `ipv4.method: auto|manual` → DHCP 状态
- `ipv4.addresses: *************/24` → 地址和网络掩码
- `ipv4.gateway: ***********` → 网关

### 2. systemd-networkd 解析

从 `.network` 文件中解析：

```ini
[Network]
DHCP=yes
Address=*************/24
Gateway=***********
```

### 3. ifupdown 解析

从配置文件中解析：

```bash
auto eth0
iface eth0 inet static
    address *************
    netmask *************
    gateway ***********
```

### 4. ip 命令解析

#### 地址信息

从 `ip addr show` 输出解析：

```
inet *************/24 brd ************* scope global eth0
```

#### 路由信息

从 `ip route show` 输出解析：

```bash
default via *********** dev eth0 proto dhcp metric 100
```

## 固定 IP 过滤机制

### 概述

系统会自动识别并过滤系统生成的固定 IP 地址，确保只返回用户手动配置的网络信息。

### 固定 IP 格式识别

- **格式**：`10.10.x.10`，其中 x 为 10-255 的数字
- **示例**：`***********`、`***********`、`************`

### 过滤逻辑

当网卡配置了多个 IP 地址时：

1. 检测每个 IP 地址是否为固定 IP 格式
2. 如果是固定 IP，则跳过该配置
3. 只返回用户手动配置的 IP 地址
4. 保留 DHCP 配置不变

### 示例场景

#### 场景 1：混合 IP 配置

假设 eth0 网卡有以下配置：

- `***********/24` (系统固定 IP - 将被过滤)
- `*************/24` (用户配置 IP - 保留)

系统将只返回：

```json
{
  "name": "eth0",
  "dhcp": false,
  "address": "*************",
  "netmask": "*************",
  "gateway": "***********"
}
```

#### 场景 2：多个用户 IP

假设 eth1 网卡有以下配置：

- `***********/24` (系统固定 IP - 将被过滤)
- `*************/24` (用户配置 IP - 保留)
- `**********/24` (用户配置 IP - 保留)

系统将返回：

```json
[
  {
    "name": "eth1",
    "dhcp": false,
    "address": "*************",
    "netmask": "*************",
    "gateway": ""
  },
  {
    "name": "eth1",
    "dhcp": false,
    "address": "**********",
    "netmask": "*************",
    "gateway": "********"
  }
]
```

#### 场景 3：仅固定 IP

如果网卡只有固定 IP：

- `***********/24` (系统固定 IP - 将被过滤)

系统将返回基本配置：

```json
{
  "name": "eth2",
  "dhcp": false,
  "address": "",
  "netmask": "",
  "gateway": ""
}
```

### 日志输出

过滤过程中会输出日志信息：

```text
Skipping fixed IP *********** for interface eth0
Skipping fixed IP *********** for interface eth1
```

## 错误处理

### 1. 网络管理工具检测失败

如果无法检测到已知的网络管理工具，系统会：

- 回退到使用 `ip` 命令
- 记录警告信息
- 返回当前网络状态

### 2. 配置文件读取失败

如果无法读取配置文件，系统会：

- 记录警告信息
- 返回基本的接口信息（仅包含接口名）
- 继续处理其他接口

### 3. 命令执行失败

如果网络命令执行失败，系统会：

- 记录详细的错误信息
- 跳过该接口的配置获取
- 继续处理其他接口

## 注意事项

### 1. 权限要求

- 容器需要以特权模式运行
- 需要访问宿主机的网络命名空间
- 需要读取宿主机的配置文件

### 2. 兼容性

- 支持主流的 Linux 发行版
- 自动适配不同的网络管理工具
- 提供统一的接口和返回格式

### 3. 性能考虑

- 每次调用都会重新检测网络管理工具
- 配置获取是实时的，反映当前状态
- 对于大量接口可能需要一定时间

## 故障排除

### 1. 检查网络管理工具

```bash
# 检查 NetworkManager
systemctl is-active NetworkManager

# 检查 systemd-networkd
systemctl is-active systemd-networkd

# 检查 ifupdown
cat /etc/network/interfaces
```

### 2. 手动测试命令

```bash
# NetworkManager
nmcli device show eth0
nmcli connection show

# systemd-networkd
ls /etc/systemd/network/

# ifupdown
cat /etc/network/interfaces.d/eth0

# ip 命令
ip addr show eth0
ip route show dev eth0
```

### 3. 查看日志

检查应用日志中的警告和错误信息，了解配置获取过程中的问题。
